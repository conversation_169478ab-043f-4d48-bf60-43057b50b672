---
import Layout from "./Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import { SITE } from "@/config";

export interface Props {
  title?: string;
  description?: string;
  ogImage?: string;
  canonicalURL?: string;
}

const {
  title = "Tools - " + SITE.title,
  description = "Online tools and utilities for various tasks",
  ogImage = SITE.ogImage,
  canonicalURL = new URL(Astro.url.pathname, Astro.url).toString(),
} = Astro.props;
---

<Layout
  title={title}
  description={description}
  ogImage={ogImage}
  canonicalURL={canonicalURL}
>
  <Header />
  <main id="main-content" class="mx-auto max-w-3xl px-4 pb-12">
    <slot />
  </main>
  <Footer />
</Layout>
