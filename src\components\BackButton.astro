---
import LinkButton from "./LinkButton.astro";
import { SITE } from "@/config";
---

{
  SITE.showBackButton && (
    <div class="mx-auto flex w-full max-w-3xl items-center justify-start px-2">
      <LinkButton
        id="back-button"
        href="/"
        class="focus-outline mt-8 mb-2 flex hover:text-foreground/75"
      >
        <svg class="inline-block size-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M15 6l-6 6l6 6" />
        </svg>
        <span>Go back</span>
      </LinkButton>
    </div>
  )
}

<script>
  /* Update Search Praam */
  function updateGoBackUrl() {
    const backButton: HTMLAnchorElement | null =
      document.querySelector("#back-button");

    const backUrl = sessionStorage.getItem("backUrl");

    if (backUrl && backButton) {
      backButton.href = backUrl;
    }
  }

  document.addEventListener("astro:page-load", updateGoBackUrl);
  updateGoBackUrl();
</script>
