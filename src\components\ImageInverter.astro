---
import FileUpload from "./FileUpload.astro";
---

<div class="image-inverter">
  <!-- File Upload Section -->
  <div class="mb-8">
    <h2 class="text-xl font-semibold mb-4">Upload Image</h2>
    <FileUpload id="image-upload" onFileSelect="handleImageUpload" />
  </div>

  <!-- Processing Section -->
  <div id="processing-section" class="hidden">
    <div class="grid gap-6 md:grid-cols-2">
      <!-- Original Image -->
      <div class="space-y-3">
        <h3 class="text-lg font-medium">Original Image</h3>
        <div class="border border-border rounded-lg overflow-hidden bg-muted/20">
          <img id="original-image" class="w-full h-auto max-h-96 object-contain" alt="Original image" />
        </div>
      </div>

      <!-- Inverted Image -->
      <div class="space-y-3">
        <h3 class="text-lg font-medium">Inverted Image</h3>
        <div class="border border-border rounded-lg overflow-hidden bg-muted/20">
          <canvas id="inverted-canvas" class="w-full h-auto max-h-96"></canvas>
        </div>
      </div>
    </div>

    <!-- Controls -->
    <div class="mt-6 flex flex-wrap gap-4 justify-center">
      <button
        id="process-btn"
        class="px-6 py-2 bg-accent text-background rounded-lg hover:bg-accent/90 transition-colors font-medium"
      >
        Invert Colors
      </button>

      <button
        id="download-btn"
        class="px-6 py-2 border border-accent text-accent rounded-lg hover:bg-accent hover:text-background transition-colors font-medium hidden"
      >
        Download Result
      </button>

      <button
        id="reset-btn"
        class="px-6 py-2 border border-border text-foreground rounded-lg hover:bg-muted/20 transition-colors font-medium"
      >
        Upload New Image
      </button>
    </div>

    <!-- Processing Status -->
    <div id="processing-status" class="mt-4 text-center text-sm text-foreground/70 hidden">
      Processing image...
    </div>
  </div>
</div>

<script>
  let currentFile: File | null = null;
  let originalImage: HTMLImageElement | null = null;
  let canvas: HTMLCanvasElement | null = null;
  let ctx: CanvasRenderingContext2D | null = null;

  function initImageInverter() {
    canvas = document.getElementById('inverted-canvas') as HTMLCanvasElement;
    ctx = canvas?.getContext('2d') || null;
    originalImage = document.getElementById('original-image') as HTMLImageElement;

    const processBtn = document.getElementById('process-btn') as HTMLButtonElement;
    const downloadBtn = document.getElementById('download-btn') as HTMLButtonElement;
    const resetBtn = document.getElementById('reset-btn') as HTMLButtonElement;

    // Process button click
    processBtn?.addEventListener('click', invertImage);

    // Download button click
    downloadBtn?.addEventListener('click', downloadImage);

    // Reset button click
    resetBtn?.addEventListener('click', resetTool);
  }

  // Global function for file upload callback
  (window as any).handleImageUpload = function(file: File) {
    currentFile = file;
    const reader = new FileReader();

    reader.onload = function(e) {
      if (originalImage && e.target?.result) {
        originalImage.src = e.target.result as string;
        originalImage.onload = function() {
          if (canvas && originalImage) {
            // Set canvas size to match image
            canvas.width = originalImage.naturalWidth;
            canvas.height = originalImage.naturalHeight;

            // Show processing section
            document.getElementById('processing-section')?.classList.remove('hidden');

            // Hide download button initially
            document.getElementById('download-btn')?.classList.add('hidden');
          }
        };
      }
    };

    reader.readAsDataURL(file);
  };

  function invertImage() {
    if (!originalImage?.src) return;

    const processBtn = document.getElementById('process-btn') as HTMLButtonElement;
    const downloadBtn = document.getElementById('download-btn') as HTMLButtonElement;
    const processingStatus = document.getElementById('processing-status');

    // Show processing status
    processingStatus?.classList.remove('hidden');
    if (processBtn) {
      processBtn.disabled = true;
      processBtn.textContent = 'Processing...';
    }

    // Use setTimeout to allow UI to update
    setTimeout(() => {
      try {
        if (ctx && canvas && originalImage) {
          // Draw original image to canvas
          ctx.drawImage(originalImage, 0, 0);

          // Get image data
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;

          // Invert colors
          for (let i = 0; i < data.length; i += 4) {
            data[i] = 255 - data[i];     // Red
            data[i + 1] = 255 - data[i + 1]; // Green
            data[i + 2] = 255 - data[i + 2]; // Blue
            // Alpha channel (data[i + 3]) remains unchanged
          }

          // Put inverted image data back to canvas
          ctx.putImageData(imageData, 0, 0);

          // Show download button
          downloadBtn?.classList.remove('hidden');
        }

      } catch (error) {
        console.error('Error processing image:', error);
        alert('Error processing image. Please try again.');
      } finally {
        // Hide processing status
        processingStatus?.classList.add('hidden');
        if (processBtn) {
          processBtn.disabled = false;
          processBtn.textContent = 'Invert Colors';
        }
      }
    }, 100);
  }

  function downloadImage() {
    if (!canvas) return;

    // Create download link
    const link = document.createElement('a');
    link.download = `inverted-${currentFile ? currentFile.name : 'image.png'}`;
    link.href = canvas.toDataURL();

    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  function resetTool() {
    // Hide processing section
    document.getElementById('processing-section')?.classList.add('hidden');

    // Clear file input
    const fileInput = document.getElementById('image-upload') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }

    // Reset variables
    currentFile = null;

    // Clear canvas
    if (ctx && canvas) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
  }

  // Initialize on page load
  initImageInverter();

  // Re-initialize on view transitions
  document.addEventListener('astro:after-swap', initImageInverter);
</script>
