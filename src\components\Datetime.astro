---
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import IconCalendar from "@/assets/icons/IconCalendar.svg";
import { SITE } from "@/config";

dayjs.extend(utc);
dayjs.extend(timezone);

export interface Props {
  class?: string;
  size?: "sm" | "lg";
  timezone: string | undefined;
  pubDatetime: string | Date;
  modDatetime: string | Date | undefined | null;
}

const {
  pubDatetime,
  modDatetime,
  size = "sm",
  class: className = "",
  timezone: postTimezone,
} = Astro.props;

/* ========== Formatted Datetime ========== */
const latestDatetime =
  modDatetime && modDatetime > pubDatetime ? modDatetime : pubDatetime;
const datetime = dayjs(latestDatetime).tz(postTimezone || SITE.timezone);

const date = datetime.format("D MMM, YYYY"); // e.g., '22 Mar, 2025'
const time = datetime.format("hh:mm A"); // e.g., '08:30 PM'
---

<div class:list={["flex items-end space-x-2 opacity-80", className]}>
  <IconCalendar
    class:list={[
      "inline-block size-6 min-w-[1.375rem]",
      { "scale-90": size === "sm" },
    ]}
  />
  {
    modDatetime && modDatetime > pubDatetime ? (
      <span class:list={["text-sm italic", { "sm:text-base": size === "lg" }]}>
        Updated:
      </span>
    ) : (
      <span class="sr-only">Published:</span>
    )
  }
  <span class:list={["text-sm italic", { "sm:text-base": size === "lg" }]}>
    <time datetime={datetime.toISOString()}>{date}</time>
    <span aria-hidden="true"> | </span>
    <span class="sr-only">&nbsp;at&nbsp;</span>
    <span class="text-nowrap">{time}</span>
  </span>
</div>
