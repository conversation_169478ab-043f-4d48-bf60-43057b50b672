---
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { SITE } from "@/config";

dayjs.extend(utc);
dayjs.extend(timezone);

export interface Props {
  class?: string;
  size?: "sm" | "lg";
  timezone: string | undefined;
  pubDatetime: string | Date;
  modDatetime: string | Date | undefined | null;
}

const {
  pubDatetime,
  modDatetime,
  size = "sm",
  class: className = "",
  timezone: postTimezone,
} = Astro.props;

/* ========== Formatted Datetime ========== */
const latestDatetime =
  modDatetime && modDatetime > pubDatetime ? modDatetime : pubDatetime;
const datetime = dayjs(latestDatetime).tz(postTimezone || SITE.timezone);

const date = datetime.format("D MMM, YYYY"); // e.g., '22 Mar, 2025'
const time = datetime.format("hh:mm A"); // e.g., '08:30 PM'
---

<div class:list={["flex items-end space-x-2 opacity-80", className]}>
  <svg
    class:list={[
      "inline-block size-6 min-w-[1.375rem]",
      { "scale-90": size === "sm" },
    ]}
    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
  >
    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
    <path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" />
    <path d="M16 3v4" />
    <path d="M8 3v4" />
    <path d="M4 11h16" />
    <path d="M7 14h.013" />
    <path d="M10.01 14h.005" />
    <path d="M13.01 14h.005" />
    <path d="M16.015 14h.005" />
    <path d="M13.015 17h.005" />
    <path d="M7.01 17h.005" />
    <path d="M10.01 17h.005" />
  </svg>
  {
    modDatetime && modDatetime > pubDatetime ? (
      <span class:list={["text-sm italic", { "sm:text-base": size === "lg" }]}>
        Updated:
      </span>
    ) : (
      <span class="sr-only">Published:</span>
    )
  }
  <span class:list={["text-sm italic", { "sm:text-base": size === "lg" }]}>
    <time datetime={datetime.toISOString()}>{date}</time>
    <span aria-hidden="true"> | </span>
    <span class="sr-only">&nbsp;at&nbsp;</span>
    <span class="text-nowrap">{time}</span>
  </span>
</div>
