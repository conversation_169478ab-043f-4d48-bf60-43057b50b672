---
import Hr from "./Hr.astro";
import Socials from "./Socials.astro";

const currentYear = new Date().getFullYear();

export interface Props {
  noMarginTop?: boolean;
}

const { noMarginTop = false } = Astro.props;
---

<footer class:list={["w-full", { "mt-auto": !noMarginTop }]}>
  <Hr noPadding />
  <div
    class="flex flex-col items-center justify-between py-6 sm:flex-row-reverse sm:py-4"
  >
    <Socials centered />
    <div class="my-2 flex flex-col items-center whitespace-nowrap sm:flex-row">
      <span>Copyright &#169; {currentYear}</span>
      <span class="hidden sm:inline">&nbsp;|&nbsp;</span>
      <span>All rights reserved.</span>
    </div>
  </div>
</footer>
