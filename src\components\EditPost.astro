---
import type { CollectionEntry } from "astro:content";
import { SITE } from "@/config";

export interface Props {
  hideEditPost?: CollectionEntry<"blog">["data"]["hideEditPost"];
  class?: string;
  post: CollectionEntry<"blog">;
}

const { hideEditPost, post, class: className = "" } = Astro.props;

const href = `${SITE.editPost.url}${post.filePath}`;
const showEditPost =
  SITE.editPost.enabled && !hideEditPost && href.trim() !== "";
---

{
  showEditPost && (
    <div class:list={["opacity-80", className]}>
      <span aria-hidden="true" class="max-sm:hidden">
        |
      </span>
      <a
        class="space-x-1.5 hover:opacity-75"
        href={href}
        rel="noopener noreferrer"
        target="_blank"
      >
        <svg class="inline-block size-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1" />
          <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z" />
          <path d="M16 5l3 3" />
        </svg>
        <span class="italic max-sm:text-sm sm:inline">
          {SITE.editPost.text}
        </span>
      </a>
    </div>
  )
}
