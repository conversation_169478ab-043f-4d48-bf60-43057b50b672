---
layout: ../layouts/AboutLayout.astro
title: "About"
---

AstroPaper is a minimal, responsive and SEO-friendly Astro blog theme. I designed and crafted this based on [my personal blog](https://satnaing.dev/blog).

This theme is aimed to be accessible out of the box. Light and dark mode are supported by
default and additional color schemes can also be configured.

This theme is self-documented \_ which means articles/posts in this theme can also be considered as documentations. So, see the documentation for more info.

<div>
  <img src="/dev.svg" class="sm:w-1/2 mx-auto" alt="coding dev illustration">
</div>

## Tech Stack

This theme is written in vanilla JavaScript (+ TypeScript for type checking) and a little bit of ReactJS for some interactions. TailwindCSS is used for styling; and Markdown is used for blog contents.

## Features

Here are certain features of this site.

- fully responsive and accessible
- SEO-friendly
- light & dark mode
- fuzzy search
- super fast performance
- draft posts
- pagination
- sitemap & rss feed
- highly customizable

If you like this theme, you can star/contribute to the [repo](https://github.com/satnaing/astro-paper).  
Or you can even give any feedback via my [email](mailto:<EMAIL>).
