---
import { <PERSON><PERSON>Router } from "astro:transitions";
import { SITE } from "@/config";
import "@/styles/global.css";

const googleSiteVerification = import.meta.env.PUBLIC_GOOGLE_SITE_VERIFICATION;

export interface Props {
  title?: string;
  author?: string;
  profile?: string;
  description?: string;
  ogImage?: string;
  canonicalURL?: string;
  pubDatetime?: Date;
  modDatetime?: Date | null;
  scrollSmooth?: boolean;
}

const {
  title = SITE.title,
  author = SITE.author,
  profile = SITE.profile,
  description = SITE.desc,
  ogImage = SITE.ogImage ? `/${SITE.ogImage}` : "/og.png",
  canonicalURL = new URL(Astro.url.pathname, Astro.url),
  pubDatetime,
  modDatetime,
  scrollSmooth = false,
} = Astro.props;

const socialImageURL = new URL(ogImage, Astro.url);

const structuredData = {
  "@context": "https://schema.org",
  "@type": "BlogPosting",
  headline: `${title}`,
  image: `${socialImageURL}`,
  datePublished: `${pubDatetime?.toISOString()}`,
  ...(modDatetime && { dateModified: modDatetime.toISOString() }),
  author: [
    {
      "@type": "Person",
      name: `${author}`,
      ...(profile && { url: profile }),
    },
  ],
};
---

<!doctype html>
<html lang=`${SITE.lang ?? "en"}` class={`${scrollSmooth && "scroll-smooth"}`}>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="canonical" href={canonicalURL} />
    <meta name="generator" content={Astro.generator} />

    <!-- General Meta Tags -->
    <title>{title}</title>
    <meta name="title" content={title} />
    <meta name="description" content={description} />
    <meta name="author" content={author} />
    <link rel="sitemap" href="/sitemap-index.xml" />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:image" content={socialImageURL} />

    <!-- Article Published/Modified time -->
    {
      pubDatetime && (
        <meta
          property="article:published_time"
          content={pubDatetime.toISOString()}
        />
      )
    }
    {
      modDatetime && (
        <meta
          property="article:modified_time"
          content={modDatetime.toISOString()}
        />
      )
    }

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalURL} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={socialImageURL} />

    <!-- Google JSON-LD Structured data -->
    <script
      type="application/ld+json"
      is:inline
      set:html={JSON.stringify(structuredData)}
    />

    <!-- Enable RSS feed auto-discovery  -->
    <!-- https://docs.astro.build/en/recipes/rss/#enabling-rss-feed-auto-discovery -->
    <link
      rel="alternate"
      type="application/rss+xml"
      title={SITE.title}
      href={new URL("rss.xml", Astro.site)}
    />

    <meta name="theme-color" content="" />

    {
      // If PUBLIC_GOOGLE_SITE_VERIFICATION is set in the environment variable,
      // include google-site-verification tag in the heading
      // Learn more: https://support.google.com/webmasters/answer/9008080#meta_tag_verification&zippy=%2Chtml-tag
      googleSiteVerification && (
        <meta
          name="google-site-verification"
          content={googleSiteVerification}
        />
      )
    }

    <ClientRouter />

    <script is:inline src="/toggle-theme.js"></script>
  </head>
  <body>
    <slot />
  </body>
</html>

<style>
  html,
  body {
    margin: 0;
    width: 100%;
    height: 100%;
  }
</style>
