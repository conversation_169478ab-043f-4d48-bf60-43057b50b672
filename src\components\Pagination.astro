---
import type { Page } from "astro";
import type { CollectionEntry } from "astro:content";
import LinkButton from "./LinkButton.astro";

export interface Props {
  page: Page<CollectionEntry<"blog">>;
}

const { page } = Astro.props;
---

{
  page.lastPage > 1 && (
    <nav class="mt-auto mb-8 flex justify-center" aria-label="Pagination">
      <LinkButton
        disabled={!page.url.prev}
        href={page.url.prev as string}
        class:list={["mr-4 select-none", { "opacity-50": !page.url.prev }]}
        ariaLabel="Previous"
      >
        <svg class="inline-block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M5 12l14 0" />
          <path d="M5 12l6 6" />
          <path d="M5 12l6 -6" />
        </svg>
        Prev
      </LinkButton>
      {page.currentPage} / {page.lastPage}
      <LinkButton
        disabled={!page.url.next}
        href={page.url.next as string}
        class:list={["ml-4 select-none", { "opacity-50": !page.url.next }]}
        ariaLabel="Next"
      >
        Next
        <svg class="inline-block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M5 12l14 0" />
          <path d="M13 18l6 -6" />
          <path d="M13 6l6 6" />
        </svg>
      </LinkButton>
    </nav>
  )
}
