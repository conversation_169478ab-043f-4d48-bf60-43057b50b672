---
import { SHARE_LINKS } from "@/constants";
import LinkButton from "./LinkButton.astro";

const URL = Astro.url;
---

<div
  class="flex flex-col flex-wrap items-center justify-center gap-1 sm:items-start"
>
  <span class="italic">Share this post on:</span>
  <div class="text-center">
    {
      SHARE_LINKS.map(social => (
        <LinkButton
          href={`${social.href + URL}`}
          class="scale-90 p-2 hover:rotate-6 sm:p-1"
          title={social.linkTitle}
        >
          <Fragment set:html={social.icon} />
          <span class="sr-only">{social.linkTitle}</span>
        </LinkButton>
      ))
    }
  </div>
</div>
