---
import { getCollection } from "astro:content";
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import Socials from "@/components/Socials.astro";
import LinkButton from "@/components/LinkButton.astro";
import Card from "@/components/Card.astro";
import Hr from "@/components/Hr.astro";
import getSortedPosts from "@/utils/getSortedPosts";
import IconArrowRight from "@/assets/icons/IconArrowRight.svg";
import { SITE } from "@/config";
import { SOCIALS } from "@/constants";

const posts = await getCollection("blog");

const sortedPosts = getSortedPosts(posts);
const featuredPosts = sortedPosts.filter(({ data }) => data.featured);
const recentPosts = sortedPosts.filter(({ data }) => !data.featured);
---

<Layout>
  <Header />
  <main id="main-content" data-layout="index">
    <section id="hero" class="pt-8 pb-6">
      <h1 class="my-4 inline-block text-4xl font-bold sm:my-8 sm:text-5xl">
        Welcome to Invert colors online
      </h1>

      <p>
        Your go-to collection of online English tools and utilities.
        Process images, manipulate text, and perform various tasks - all in your browser
        with complete privacy and security.
      </p>
      <p class="mt-2">
        Start with our
        <LinkButton
          class="underline decoration-dashed underline-offset-4 hover:text-accent"
          href="/tools/image-inverter"
        >
          Image Color Inverter
        </LinkButton> or explore all
        <LinkButton
          class="underline decoration-dashed underline-offset-4 hover:text-accent"
          href="/tools"
        >
          available tools
        </LinkButton>.
      </p>
      {
        // only display if at least one social link is enabled
        SOCIALS.length > 0 && (
          <div class="mt-4 flex flex-col sm:flex-row sm:items-center">
            <div class="mr-2 mb-1 whitespace-nowrap sm:mb-0">Social Links:</div>
            <Socials />
          </div>
        )
      }
    </section>

    <Hr />

    <!-- Featured Tools Section -->
    <section id="featured-tools" class="pt-12 pb-6">
      <h2 class="text-2xl font-semibold tracking-wide">Featured Tools</h2>
      <div class="mt-6 grid gap-6 md:grid-cols-2">
        <div class="border border-border rounded-lg p-6 hover:bg-muted/10 transition-colors">
          <h3 class="text-xl font-semibold mb-3 text-accent">🎨 Image Color Inverter</h3>
          <p class="text-foreground/80 mb-4">
            Transform your images by inverting their colors to create stunning negative effects.
            Perfect for artistic projects, accessibility needs, or just for fun!
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="text-xs px-2 py-1 bg-accent/10 text-accent rounded">Privacy First</span>
            <span class="text-xs px-2 py-1 bg-accent/10 text-accent rounded">No Upload</span>
            <span class="text-xs px-2 py-1 bg-accent/10 text-accent rounded">Instant Results</span>
          </div>
          <LinkButton
            href="/tools/image-inverter"
            class="inline-block px-4 py-2 bg-accent text-background rounded hover:bg-accent/90 transition-colors"
          >
            Try Now →
          </LinkButton>
        </div>

        <div class="border border-border rounded-lg p-6 opacity-60">
          <h3 class="text-xl font-semibold mb-3">🚀 More Tools Coming Soon</h3>
          <p class="text-foreground/80 mb-4">
            We're working on adding more useful tools including text processors,
            color utilities, converters, and much more!
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="text-xs px-2 py-1 bg-muted/20 text-foreground/60 rounded">Text Tools</span>
            <span class="text-xs px-2 py-1 bg-muted/20 text-foreground/60 rounded">Converters</span>
            <span class="text-xs px-2 py-1 bg-muted/20 text-foreground/60 rounded">Utilities</span>
          </div>
          <LinkButton
            href="/tools"
            class="inline-block px-4 py-2 border border-border text-foreground rounded hover:bg-muted/20 transition-colors"
          >
            View All Tools
          </LinkButton>
        </div>
      </div>
    </section>

    <Hr />

    {
      featuredPosts.length > 0 && (
        <>
          <section id="featured" class="pt-12 pb-6">
            <h2 class="text-2xl font-semibold tracking-wide">Featured</h2>
            <ul>
              {featuredPosts.map(data => (
                <Card variant="h3" {...data} />
              ))}
            </ul>
          </section>
          {recentPosts.length > 0 && <Hr />}
        </>
      )
    }

    {
      recentPosts.length > 0 && (
        <section id="recent-posts" class="pt-12 pb-6">
          <h2 class="text-2xl font-semibold tracking-wide">Recent Posts</h2>
          <ul>
            {recentPosts.map(
              (data, index) =>
                index < SITE.postPerIndex && <Card variant="h3" {...data} />
            )}
          </ul>
        </section>
      )
    }

    <div class="my-8 text-center">
      <LinkButton href="/posts/">
        All Posts
        <IconArrowRight class="inline-block" />
      </LinkButton>
    </div>
  </main>
  <Footer />
</Layout>

<script>
  document.addEventListener("astro:page-load", () => {
    const indexLayout = (document.querySelector("#main-content") as HTMLElement)
      ?.dataset?.layout;
    if (indexLayout) {
      sessionStorage.setItem("backUrl", "/");
    }
  });
</script>
