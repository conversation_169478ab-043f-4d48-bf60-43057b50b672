---
export interface Props {
  tag: string;
  tagName: string;
  size?: "sm" | "lg";
}

const { tag, tagName, size = "sm" } = Astro.props;
---

<li
  class:list={[
    "group inline-block group-hover:cursor-pointer",
    size === "sm" ? "my-1 underline-offset-4" : "mx-1 my-3 underline-offset-8",
  ]}
>
  <a
    href={`/tags/${tag}/`}
    transition:name={tag}
    class:list={[
      "relative pr-2 text-lg underline decoration-dashed group-hover:-top-0.5 group-hover:text-accent focus-visible:p-1",
      { "text-sm": size === "sm" },
    ]}
  >
    <svg
      class:list={[
        "inline-block opacity-80",
        { "-mr-3.5 size-4": size === "sm" },
        { "-mr-5 size-6": size === "lg" },
      ]}
      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
    >
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M5 9l14 0" />
      <path d="M5 15l14 0" />
      <path d="M11 4l-4 16" />
      <path d="M17 4l-4 16" />
    </svg>
    &nbsp;<span>{tagName}</span>
  </a>
</li>
